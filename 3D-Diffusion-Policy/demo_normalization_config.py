#!/usr/bin/env python3
"""
Demonstration script showing how to use different normalization configurations
with the Uni3D encoder and how weight loading works for each mode.
"""

import torch
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'diffusion_policy_3d'))

from diffusion_policy_3d.model.vision.pointnet_extractor import Uni3DPointcloudEncoder

def demo_normalization_configurations():
    """Demonstrate different normalization configurations."""
    
    print("=" * 80)
    print("Uni3D Encoder Normalization Configuration Demo")
    print("=" * 80)
    
    # Base configuration for Uni3D encoder
    base_config = {
        'pc_model': 'eva02_tiny_patch14_224',
        'pc_feat_dim': 192,
        'embed_dim': 512,
        'group_size': 32,
        'num_group': 64,  # Smaller for demo
        'patch_dropout': 0.0,
        'drop_path_rate': 0.0,
        'pretrained_pc': None,
        'pc_encoder_dim': 512,
        'use_pretrained_weights': False,  # Set to False for demo
        'pretrained_weights_path': None,
    }
    
    # Test input
    batch_size = 2
    num_points = 1024
    test_input = torch.randn(batch_size, num_points, 6)  # (batch, points, xyz+rgb)
    
    print(f"Test input shape: {test_input.shape}")
    print()
    
    # Test each normalization configuration
    normalization_configs = [
        {
            'name': 'BatchNorm (Default)',
            'type': 'batch_norm',
            'description': 'Uses BatchNorm1d layers - compatible with pre-trained Uni3D weights'
        },
        {
            'name': 'LayerNorm',
            'type': 'layer_norm', 
            'description': 'Uses LayerNorm layers - conv weights can be loaded from pre-trained models'
        },
        {
            'name': 'No Normalization',
            'type': 'none',
            'description': 'No normalization layers - conv weights can be loaded from pre-trained models'
        }
    ]
    
    results = {}
    
    for config in normalization_configs:
        print(f"Testing: {config['name']}")
        print(f"Description: {config['description']}")
        print("-" * 60)
        
        try:
            # Create encoder with specific normalization
            encoder_config = base_config.copy()
            encoder_config['normalization_type'] = config['type']
            
            encoder = Uni3DPointcloudEncoder(**encoder_config)
            encoder.eval()
            
            # Forward pass
            with torch.no_grad():
                output = encoder(test_input, eval=True)
            
            # Store results
            results[config['type']] = {
                'success': True,
                'output_shape': output.shape,
                'output_mean': output.mean().item(),
                'output_std': output.std().item(),
                'num_parameters': sum(p.numel() for p in encoder.parameters()),
                'encoder_norm_type': encoder.encoder.normalization_type
            }
            
            print(f"✓ Success!")
            print(f"  Output shape: {output.shape}")
            print(f"  Output statistics: mean={output.mean().item():.4f}, std={output.std().item():.4f}")
            print(f"  Total parameters: {results[config['type']]['num_parameters']:,}")
            print(f"  Encoder normalization: {encoder.encoder.normalization_type}")
            
        except Exception as e:
            results[config['type']] = {'success': False, 'error': str(e)}
            print(f"✗ Failed: {str(e)}")
        
        print()
    
    # Summary
    print("=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    successful_configs = [k for k, v in results.items() if v.get('success', False)]
    
    if len(successful_configs) == len(normalization_configs):
        print("🎉 All normalization configurations work correctly!")
    else:
        print(f"⚠️  {len(successful_configs)}/{len(normalization_configs)} configurations successful")
    
    print()
    print("Configuration Comparison:")
    print("-" * 40)
    
    for config in normalization_configs:
        config_type = config['type']
        if config_type in results and results[config_type].get('success'):
            result = results[config_type]
            print(f"{config['name']:20} | Parameters: {result['num_parameters']:8,} | "
                  f"Output: {result['output_shape']}")
        else:
            print(f"{config['name']:20} | FAILED")
    
    print()
    print("Weight Loading Compatibility:")
    print("-" * 40)
    print("batch_norm:  ✓ Full compatibility with pre-trained Uni3D weights")
    print("layer_norm:  ✓ Conv weights loaded, LayerNorm initialized randomly")  
    print("none:        ✓ Conv weights loaded, no normalization layers")
    
    print()
    print("Usage Examples:")
    print("-" * 40)
    print("# In your YAML config file:")
    print("pointcloud_encoder_cfg:")
    print("  # ... other config ...")
    print("  normalization_type: \"batch_norm\"  # or \"layer_norm\" or \"none\"")
    print()
    print("# In Python code:")
    print("encoder = Uni3DPointcloudEncoder(")
    print("    # ... other params ...")
    print("    normalization_type=\"layer_norm\",")
    print("    use_pretrained_weights=True,")
    print("    pretrained_weights_path=\"path/to/weights.pt\"")
    print(")")

if __name__ == "__main__":
    demo_normalization_configurations()
