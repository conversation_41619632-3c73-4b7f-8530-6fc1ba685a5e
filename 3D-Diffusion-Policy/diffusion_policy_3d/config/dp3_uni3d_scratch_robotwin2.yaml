defaults:
  - task: robotwin2_demo_task

name: train_dp3_uni3d

task_name: null
shape_meta: ${task.shape_meta}
exp_name: "dp3"
setting: none

# horizon: 4
horizon: 16
n_obs_steps: 2
# n_action_steps: 3
n_action_steps: 8
n_latency_steps: 0
dataset_obs_steps: ${n_obs_steps}
keypoint_visible_rate: 1.0
obs_as_global_cond: True

policy:
  _target_: diffusion_policy_3d.policy.dp3.DP3
  use_point_crop: true
  condition_type: film
  use_down_condition: true
  use_mid_condition: true
  use_up_condition: true
  
  diffusion_step_embed_dim: 128
  down_dims:
  - 512
  - 1024
  - 2048
  crop_shape:
  - 80
  - 80
  encoder_output_dim: 1024
  horizon: ${horizon}
  kernel_size: 5
  n_action_steps: ${n_action_steps}
  n_groups: 8
  n_obs_steps: ${n_obs_steps}

  noise_scheduler:
    _target_: diffusers.schedulers.scheduling_ddim.DDIMScheduler
    num_train_timesteps: 100
    beta_start: 0.0001
    beta_end: 0.02
    beta_schedule: squaredcos_cap_v2
    clip_sample: True
    set_alpha_to_one: True
    steps_offset: 0
    prediction_type: sample


  num_inference_steps: 10
  obs_as_global_cond: true
  shape_meta: ${shape_meta}

  use_pc_color: false
  pointnet_type: "uni3d"


  pointcloud_encoder_cfg:
    pc_model: "eva02_tiny_patch14_224"
    pc_feat_dim: 192
    embed_dim: 1024
    group_size: 32
    num_group: 512
    patch_dropout: 0.5
    drop_path_rate: 0.2
    pretrained_pc: null # Uni3D下创建的point transformer本身也就是空的
    pc_encoder_dim: 512
    use_pretrained_weights: false
    pretrained_weights_path: null  # 预训练权重路径
    normalization_type: "layer_norm"  # Options: "batch_norm", "layer_norm", "none"

data_augmentation:
  use_augmentation: false      # 控制是否启用数据增强
  pc_noise_std: 0.002         # 点云噪声的标准差
  agent_pos_noise_std: 0.0002  # agent_pos 噪声的标准差


ema:
  _target_: diffusion_policy_3d.model.diffusion.ema_model.EMAModel
  update_after_step: 0
  inv_gamma: 1.0
  power: 0.75
  min_value: 0.0
  max_value: 0.9999

dataloader:
  batch_size: 128 # originally 128, modified for not out of memory
  num_workers: 8
  shuffle: True
  pin_memory: True
  persistent_workers: False

val_dataloader:
  batch_size: 128 # originally 128, modified for not out of memory
  num_workers: 8
  shuffle: False
  pin_memory: True
  persistent_workers: False

optimizer:
  _target_: torch.optim.AdamW
  lr: 1.0e-4
  betas: [0.95, 0.999]
  eps: 1.0e-8
  weight_decay: 1.0e-6

training:
  device: "cuda:0"
  seed: 42
  debug: False
  resume: True
  lr_scheduler: cosine
  lr_warmup_steps: 500
  num_epochs: 3001
  gradient_accumulate_every: 2
  use_ema: True
  rollout_every: 200
  checkpoint_every: 50
  val_every: 50
  sample_every: 5
  max_train_steps: null
  max_val_steps: null
  tqdm_interval_sec: 1.0

logging:
  group: ${exp_name}
  id: null
  mode: online
  name: ${task.name}
  project: dp3_uni3d_scratch
  resume: true
  tags:
  - dp3
  - uni3d
  - from_scratch

checkpoint:
  save_ckpt: False # if True, save checkpoint every checkpoint_every
  topk:
    monitor_key: test_mean_score
    mode: max
    k: 1
    format_str: 'epoch={epoch:04d}-test_mean_score={test_mean_score:.3f}.ckpt'
  save_last_ckpt: True # this only saves when save_ckpt is True
  save_last_snapshot: False

multi_run:
  run_dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  wandb_name_base: ${now:%Y.%m.%d-%H.%M.%S}_${name}_${task_name}

hydra:
  job:
    override_dirname: ${name}
  run:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  sweep:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
    subdir: ${hydra.job.num}
