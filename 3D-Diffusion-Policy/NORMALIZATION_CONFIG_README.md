# Configurable Normalization for Uni3D Encoder

This document describes the configurable normalization system implemented for the Uni3D point cloud encoder, which allows flexible normalization choices while maintaining compatibility with pre-trained weights.

## Overview

The `Encoder` class in `diffusion_policy_3d/model/vision/pointnet_extractor.py` now supports three different normalization modes:

1. **`"batch_norm"`** - Uses BatchNorm1d layers (default, fully compatible with pre-trained Uni3D weights)
2. **`"layer_norm"`** - Uses LayerNorm layers with custom 1D wrapper
3. **`"none"`** - No normalization layers

## Configuration

### YAML Configuration

Add the `normalization_type` parameter to your configuration file:

```yaml
pointcloud_encoder_cfg:
  pc_model: "eva02_tiny_patch14_224"
  pc_feat_dim: 192
  embed_dim: 1024
  # ... other parameters ...
  normalization_type: "batch_norm"  # Options: "batch_norm", "layer_norm", "none"
```

### Python Configuration

```python
from diffusion_policy_3d.model.vision.pointnet_extractor import Uni3DPointcloudEncoder

encoder = Uni3DPointcloudEncoder(
    pc_model='eva02_tiny_patch14_224',
    pc_feat_dim=192,
    embed_dim=1024,
    # ... other parameters ...
    normalization_type="layer_norm",  # Choose your normalization type
    use_pretrained_weights=True,
    pretrained_weights_path="path/to/weights.pt"
)
```

## Weight Loading Behavior

The system implements intelligent selective weight loading based on the normalization configuration:

### BatchNorm Mode (`"batch_norm"`)
- **Full Compatibility**: Loads all pre-trained weights completely
- **Use Case**: When you want exact compatibility with original Uni3D models
- **Behavior**: All convolutional and BatchNorm parameters are loaded from pre-trained weights

### LayerNorm Mode (`"layer_norm"`)
- **Selective Loading**: Loads only convolutional layer weights
- **Use Case**: When you want different normalization but benefit from pre-trained conv features
- **Behavior**: 
  - ✅ Convolutional layer weights loaded from pre-trained model
  - ❌ BatchNorm parameters skipped (incompatible)
  - 🔄 LayerNorm parameters randomly initialized and trained from scratch

### None Mode (`"none"`)
- **Selective Loading**: Loads only convolutional layer weights
- **Use Case**: When you want no normalization but benefit from pre-trained conv features
- **Behavior**:
  - ✅ Convolutional layer weights loaded from pre-trained model
  - ❌ BatchNorm parameters skipped (not needed)
  - 🔄 No normalization layers to initialize

## Implementation Details

### Custom LayerNorm1d

For 1D convolutions, we implement a custom LayerNorm wrapper that handles the tensor dimension requirements:

```python
class LayerNorm1d(nn.Module):
    def __init__(self, num_features):
        super().__init__()
        self.layer_norm = nn.LayerNorm(num_features)
    
    def forward(self, x):
        # x shape: (batch_size, channels, sequence_length)
        x = x.transpose(1, 2)  # -> (batch_size, sequence_length, channels)
        x = self.layer_norm(x)
        x = x.transpose(1, 2)  # -> (batch_size, channels, sequence_length)
        return x
```

### Selective Weight Loading

The `_load_pretrained_weights_selective` method handles different loading strategies:

1. **Key Processing**: Handles Uni3D weight key transformations
2. **Shape Validation**: Ensures weight shapes match current model
3. **Selective Filtering**: Skips incompatible normalization weights for non-BatchNorm modes
4. **Error Handling**: Graceful fallback to random initialization if loading fails

## Testing

Run the comprehensive test suite:

```bash
cd 3D-Diffusion-Policy
python test_encoder_normalization.py
```

Run the demonstration:

```bash
cd 3D-Diffusion-Policy  
python demo_normalization_config.py
```

## Benefits

1. **Backward Compatibility**: Existing BatchNorm configurations work unchanged
2. **Flexibility**: Easy to experiment with different normalization approaches
3. **Pre-trained Leverage**: Can still benefit from pre-trained conv weights even with different normalization
4. **Extensibility**: Easy to add new normalization types in the future

## Migration Guide

### From Existing Code

If you have existing code using Uni3D encoders, no changes are required. The default behavior (`normalization_type="batch_norm"`) maintains full compatibility.

### To Use LayerNorm

1. Update your config file to include `normalization_type: "layer_norm"`
2. The system will automatically load compatible weights and initialize LayerNorm layers
3. Train as usual - the LayerNorm layers will learn appropriate parameters

### To Remove Normalization

1. Update your config file to include `normalization_type: "none"`
2. The system will load convolutional weights and skip normalization entirely
3. This can be useful for studying the effect of normalization or for specific architectural experiments

## Performance Considerations

- **BatchNorm**: Fastest, most memory efficient, best for large batches
- **LayerNorm**: Slightly slower, batch-size independent normalization
- **None**: Fastest forward pass, but may require careful initialization and learning rates

Choose based on your specific use case and training requirements.
