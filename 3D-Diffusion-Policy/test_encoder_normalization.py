#!/usr/bin/env python3
"""
Test script to verify the configurable normalization in the Encoder class.
This script tests all three normalization modes: batch_norm, layer_norm, and none.
Also tests selective weight loading for different normalization configurations.
"""

import torch
import torch.nn as nn
import sys
import os
import tempfile
import shutil

# Add the project root to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'diffusion_policy_3d'))

from diffusion_policy_3d.model.vision.pointnet_extractor import Encoder, Uni3DPointcloudEncoder

def test_encoder_normalization():
    """Test the Encoder class with different normalization types."""
    
    # Test parameters
    batch_size = 4
    num_groups = 8
    num_points = 32
    input_channels = 6
    encoder_channel = 512
    
    # Create test input
    # Shape: (batch_size, num_groups, num_points, input_channels)
    test_input = torch.randn(batch_size, num_groups, num_points, input_channels)
    
    print(f"Test input shape: {test_input.shape}")
    print(f"Expected output shape: ({batch_size}, {num_groups}, {encoder_channel})")
    print("-" * 60)
    
    # Test all normalization types
    normalization_types = ["batch_norm", "layer_norm", "none"]
    
    for norm_type in normalization_types:
        print(f"\nTesting normalization type: {norm_type}")
        
        try:
            # Create encoder with specific normalization type
            encoder = Encoder(encoder_channel=encoder_channel, normalization_type=norm_type)
            encoder.eval()  # Set to evaluation mode
            
            # Forward pass
            with torch.no_grad():
                output = encoder(test_input)
            
            # Check output shape
            expected_shape = (batch_size, num_groups, encoder_channel)
            if output.shape == expected_shape:
                print(f"✓ Output shape correct: {output.shape}")
            else:
                print(f"✗ Output shape incorrect: {output.shape}, expected: {expected_shape}")
                return False
            
            # Check for NaN or Inf values
            if torch.isnan(output).any():
                print(f"✗ Output contains NaN values")
                return False
            elif torch.isinf(output).any():
                print(f"✗ Output contains Inf values")
                return False
            else:
                print(f"✓ Output values are valid (no NaN/Inf)")
            
            # Print some statistics
            print(f"  Output mean: {output.mean().item():.6f}")
            print(f"  Output std: {output.std().item():.6f}")
            print(f"  Output min: {output.min().item():.6f}")
            print(f"  Output max: {output.max().item():.6f}")
            
            # Test that the model can handle different batch sizes
            test_input_small = torch.randn(2, num_groups, num_points, input_channels)
            with torch.no_grad():
                output_small = encoder(test_input_small)
            
            expected_shape_small = (2, num_groups, encoder_channel)
            if output_small.shape == expected_shape_small:
                print(f"✓ Different batch size works: {output_small.shape}")
            else:
                print(f"✗ Different batch size failed: {output_small.shape}, expected: {expected_shape_small}")
                return False
                
        except Exception as e:
            print(f"✗ Error with {norm_type}: {str(e)}")
            return False
    
    print("\n" + "=" * 60)
    print("All normalization types tested successfully!")
    
    # Test backward compatibility - ensure batch_norm mode can load weights
    print("\nTesting backward compatibility...")
    try:
        encoder_batch_norm = Encoder(encoder_channel=encoder_channel, normalization_type="batch_norm")
        encoder_none = Encoder(encoder_channel=encoder_channel, normalization_type="none")
        
        # Get state dict from batch_norm encoder
        batch_norm_state = encoder_batch_norm.state_dict()
        
        # Try to load compatible weights into none encoder (should work for conv layers)
        none_state = encoder_none.state_dict()
        
        # Count compatible parameters
        compatible_keys = []
        for key in batch_norm_state.keys():
            if key in none_state.keys() and batch_norm_state[key].shape == none_state[key].shape:
                compatible_keys.append(key)
        
        print(f"✓ Compatible parameters between batch_norm and none: {len(compatible_keys)}")
        print(f"  Compatible keys: {compatible_keys}")
        
    except Exception as e:
        print(f"✗ Backward compatibility test failed: {str(e)}")
        return False
    
    return True

def test_layer_norm_1d():
    """Test the custom LayerNorm1d implementation."""
    print("\nTesting LayerNorm1d implementation...")
    
    # Create test data
    batch_size, channels, seq_len = 4, 128, 32
    x = torch.randn(batch_size, channels, seq_len)
    
    # Create LayerNorm1d from the Encoder class
    encoder = Encoder(encoder_channel=512, normalization_type="layer_norm")
    layer_norm_1d = None
    
    # Extract the LayerNorm1d from the first_conv sequential
    for module in encoder.first_conv:
        if hasattr(module, 'layer_norm'):
            layer_norm_1d = module
            break
    
    if layer_norm_1d is not None:
        with torch.no_grad():
            output = layer_norm_1d(x)
        
        if output.shape == x.shape:
            print(f"✓ LayerNorm1d shape preserved: {output.shape}")
        else:
            print(f"✗ LayerNorm1d shape changed: {output.shape}, expected: {x.shape}")
            return False
        
        # Check normalization properties
        # After LayerNorm, each sample should have mean ≈ 0 and std ≈ 1 along the channel dimension
        output_transposed = output.transpose(1, 2)  # (batch, seq, channels)
        means = output_transposed.mean(dim=2)  # Mean across channels
        stds = output_transposed.std(dim=2)    # Std across channels
        
        print(f"  Mean of means: {means.mean().item():.6f} (should be close to 0)")
        print(f"  Mean of stds: {stds.mean().item():.6f} (should be close to 1)")
        
        if abs(means.mean().item()) < 1e-5 and abs(stds.mean().item() - 1.0) < 0.1:
            print("✓ LayerNorm1d normalization properties correct")
        else:
            print("⚠ LayerNorm1d normalization properties may be off (this can be normal)")
    else:
        print("✗ Could not find LayerNorm1d in encoder")
        return False
    
    return True

def test_selective_weight_loading():
    """Test selective weight loading for different normalization configurations."""
    print("\nTesting selective weight loading...")

    # Create a temporary directory for test weights
    temp_dir = tempfile.mkdtemp()
    test_weights_path = os.path.join(temp_dir, "test_weights.pt")

    try:
        # Create a reference encoder with batch_norm and save its weights
        print("Creating reference weights...")
        reference_encoder = Encoder(encoder_channel=512, normalization_type="batch_norm")

        # Create some dummy state dict that mimics Uni3D structure
        dummy_state_dict = {
            'module': {
                'point_encoder.encoder.first_conv.0.weight': reference_encoder.first_conv[0].weight.clone(),
                'point_encoder.encoder.first_conv.0.bias': reference_encoder.first_conv[0].bias.clone(),
                'point_encoder.encoder.first_conv.1.weight': reference_encoder.first_conv[1].weight.clone(),
                'point_encoder.encoder.first_conv.1.bias': reference_encoder.first_conv[1].bias.clone(),
                'point_encoder.encoder.first_conv.1.running_mean': reference_encoder.first_conv[1].running_mean.clone(),
                'point_encoder.encoder.first_conv.1.running_var': reference_encoder.first_conv[1].running_var.clone(),
                'point_encoder.encoder.first_conv.3.weight': reference_encoder.first_conv[3].weight.clone(),
                'point_encoder.encoder.first_conv.3.bias': reference_encoder.first_conv[3].bias.clone(),
                'point_encoder.encoder.second_conv.0.weight': reference_encoder.second_conv[0].weight.clone(),
                'point_encoder.encoder.second_conv.0.bias': reference_encoder.second_conv[0].bias.clone(),
                'point_encoder.encoder.second_conv.1.weight': reference_encoder.second_conv[1].weight.clone(),
                'point_encoder.encoder.second_conv.1.bias': reference_encoder.second_conv[1].bias.clone(),
                'point_encoder.encoder.second_conv.1.running_mean': reference_encoder.second_conv[1].running_mean.clone(),
                'point_encoder.encoder.second_conv.1.running_var': reference_encoder.second_conv[1].running_var.clone(),
                'point_encoder.encoder.second_conv.3.weight': reference_encoder.second_conv[3].weight.clone(),
                'point_encoder.encoder.second_conv.3.bias': reference_encoder.second_conv[3].bias.clone(),
                # Add some visual transformer weights (should be loaded for all modes)
                'visual.norm.weight': torch.randn(1024),
                'visual.norm.bias': torch.randn(1024),
            }
        }

        # Save the dummy weights
        torch.save(dummy_state_dict, test_weights_path)
        print(f"✓ Test weights saved to: {test_weights_path}")

        # Test loading with different normalization types
        normalization_types = ["batch_norm", "layer_norm", "none"]

        for norm_type in normalization_types:
            print(f"\nTesting weight loading with {norm_type} normalization...")

            try:
                # Create Uni3DPointcloudEncoder with specific normalization
                config = {
                    'pc_model': 'eva02_tiny_patch14_224',
                    'pc_feat_dim': 192,
                    'embed_dim': 512,
                    'group_size': 32,
                    'num_group': 64,  # Smaller for testing
                    'patch_dropout': 0.0,  # Disable for testing
                    'drop_path_rate': 0.0,
                    'pretrained_pc': None,
                    'pc_encoder_dim': 512,
                    'use_pretrained_weights': True,
                    'pretrained_weights_path': test_weights_path,  # Use absolute path
                    'normalization_type': norm_type
                }

                # This should work without errors for all normalization types
                encoder = Uni3DPointcloudEncoder(**config)
                print(f"✓ {norm_type}: Encoder created successfully")

                # Test forward pass
                test_input = torch.randn(1, 64, 6)  # (batch, points, features)
                with torch.no_grad():
                    output = encoder(test_input, eval=True)
                print(f"✓ {norm_type}: Forward pass successful, output shape: {output.shape}")

                # Check that the encoder has the correct normalization type
                actual_norm_type = encoder.encoder.normalization_type
                if actual_norm_type == norm_type:
                    print(f"✓ {norm_type}: Normalization type correctly set")
                else:
                    print(f"✗ {norm_type}: Normalization type mismatch: {actual_norm_type} vs {norm_type}")
                    return False

            except Exception as e:
                print(f"✗ {norm_type}: Error during weight loading test: {str(e)}")
                return False

        print("\n✓ All selective weight loading tests passed!")
        return True

    except Exception as e:
        print(f"✗ Error in selective weight loading test: {str(e)}")
        return False
    finally:
        # Clean up temporary directory
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"✓ Cleaned up temporary directory: {temp_dir}")

def test_weight_compatibility():
    """Test that different normalization modes can handle the same base weights."""
    print("\nTesting weight compatibility across normalization modes...")

    try:
        # Create encoders with different normalization types
        encoder_batch = Encoder(encoder_channel=512, normalization_type="batch_norm")
        encoder_layer = Encoder(encoder_channel=512, normalization_type="layer_norm")
        encoder_none = Encoder(encoder_channel=512, normalization_type="none")

        # Get state dicts
        batch_state = encoder_batch.state_dict()
        layer_state = encoder_layer.state_dict()
        none_state = encoder_none.state_dict()

        # Find common keys (should be conv layer weights)
        common_keys = set(batch_state.keys()) & set(layer_state.keys()) & set(none_state.keys())
        conv_keys = [k for k in common_keys if 'conv' in k and ('weight' in k or 'bias' in k)]

        print(f"✓ Common conv keys found: {len(conv_keys)}")
        print(f"  Keys: {conv_keys}")

        # Test that conv weights can be transferred
        for key in conv_keys:
            if batch_state[key].shape == layer_state[key].shape == none_state[key].shape:
                print(f"✓ {key}: Compatible shapes across all normalization types")
            else:
                print(f"✗ {key}: Incompatible shapes")
                return False

        # Test partial weight loading
        print("\nTesting partial weight loading...")

        # Create a partial state dict with only conv weights
        partial_state = {k: v for k, v in batch_state.items() if k in conv_keys}

        # Try loading into layer_norm encoder
        missing_keys, unexpected_keys = encoder_layer.load_state_dict(partial_state, strict=False)
        print(f"✓ layer_norm encoder: loaded {len(partial_state)} conv weights")
        print(f"  Missing keys: {len(missing_keys)} (expected - norm layers)")

        # Try loading into none encoder
        missing_keys, unexpected_keys = encoder_none.load_state_dict(partial_state, strict=False)
        print(f"✓ none encoder: loaded {len(partial_state)} conv weights")
        print(f"  Missing keys: {len(missing_keys)} (expected - no norm layers)")

        return True

    except Exception as e:
        print(f"✗ Error in weight compatibility test: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing Encoder class with configurable normalization and selective weight loading...")
    print("=" * 80)

    success = test_encoder_normalization()
    if success:
        success = test_layer_norm_1d()
    if success:
        success = test_weight_compatibility()
    if success:
        success = test_selective_weight_loading()

    if success:
        print("\n🎉 All tests passed!")
        print("The configurable normalization system is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
