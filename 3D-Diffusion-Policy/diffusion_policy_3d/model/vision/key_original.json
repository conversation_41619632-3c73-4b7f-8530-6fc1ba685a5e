odict_keys(['cls_token', 'cls_pos', 'encoder.first_conv.0.weight', 'encoder.first_conv.0.bias', 'encoder.first_conv.1.weight', 'encoder.first_conv.1.bias', 'encoder.first_conv.1.running_mean', 'encoder.first_conv.1.running_var', 'encoder.first_conv.1.num_batches_tracked', 'encoder.first_conv.3.weight', 'encoder.first_conv.3.bias', 'encoder.second_conv.0.weight', 'encoder.second_conv.0.bias', 'encoder.second_conv.1.weight', 'encoder.second_conv.1.bias', 'encoder.second_conv.1.running_mean', 'encoder.second_conv.1.running_var', 'encoder.second_conv.1.num_batches_tracked', 'encoder.second_conv.3.weight', 'encoder.second_conv.3.bias', 'encoder2trans.weight', 'encoder2trans.bias', 'trans2embed.weight', 'trans2embed.bias', 'pos_embed.0.weight', 'pos_embed.0.bias', 'pos_embed.2.weight', 'pos_embed.2.bias', 
'visual_cls_token', 'visual_pos_embed', 'visual_patch_embed.proj.weight', 'visual_patch_embed.proj.bias', 
'visual_blocks.0.norm1.weight', 'visual_blocks.0.norm1.bias', 'visual_blocks.0.attn.q_bias', 'visual_blocks.0.attn.v_bias', 'visual_blocks.0.attn.qkv.weight', 'visual_blocks.0.attn.proj.weight', 'visual_blocks.0.attn.proj.bias', 'visual_blocks.0.norm2.weight', 'visual_blocks.0.norm2.bias', 'visual_blocks.0.mlp.fc1.weight', 'visual_blocks.0.mlp.fc1.bias', 'visual_blocks.0.mlp.fc2.weight', 'visual_blocks.0.mlp.fc2.bias', 'visual_blocks.1.norm1.weight', 'visual_blocks.1.norm1.bias', 'visual_blocks.1.attn.q_bias', 'visual_blocks.1.attn.v_bias', 'visual_blocks.1.attn.qkv.weight', 'visual_blocks.1.attn.proj.weight', 'visual_blocks.1.attn.proj.bias', 'visual_blocks.1.norm2.weight', 'visual_blocks.1.norm2.bias', 'visual_blocks.1.mlp.fc1.weight', 'visual_blocks.1.mlp.fc1.bias', 'visual_blocks.1.mlp.fc2.weight', 'visual_blocks.1.mlp.fc2.bias', 'visual_blocks.2.norm1.weight', 'visual_blocks.2.norm1.bias', 'visual_blocks.2.attn.q_bias', 'visual_blocks.2.attn.v_bias', 'visual_blocks.2.attn.qkv.weight', 'visual_blocks.2.attn.proj.weight', 'visual_blocks.2.attn.proj.bias', 'visual_blocks.2.norm2.weight', 'visual_blocks.2.norm2.bias', 'visual_blocks.2.mlp.fc1.weight', 'visual_blocks.2.mlp.fc1.bias', 'visual_blocks.2.mlp.fc2.weight', 'visual_blocks.2.mlp.fc2.bias', 'visual_blocks.3.norm1.weight', 'visual_blocks.3.norm1.bias', 'visual_blocks.3.attn.q_bias', 'visual_blocks.3.attn.v_bias', 'visual_blocks.3.attn.qkv.weight', 'visual_blocks.3.attn.proj.weight', 'visual_blocks.3.attn.proj.bias', 'visual_blocks.3.norm2.weight', 'visual_blocks.3.norm2.bias', 'visual_blocks.3.mlp.fc1.weight', 'visual_blocks.3.mlp.fc1.bias', 'visual_blocks.3.mlp.fc2.weight', 'visual_blocks.3.mlp.fc2.bias', 'visual_blocks.4.norm1.weight', 'visual_blocks.4.norm1.bias', 'visual_blocks.4.attn.q_bias', 'visual_blocks.4.attn.v_bias', 'visual_blocks.4.attn.qkv.weight', 'visual_blocks.4.attn.proj.weight', 'visual_blocks.4.attn.proj.bias', 'visual_blocks.4.norm2.weight', 'visual_blocks.4.norm2.bias', 'visual_blocks.4.mlp.fc1.weight', 'visual_blocks.4.mlp.fc1.bias', 'visual_blocks.4.mlp.fc2.weight', 'visual_blocks.4.mlp.fc2.bias', 'visual_blocks.5.norm1.weight', 'visual_blocks.5.norm1.bias', 'visual_blocks.5.attn.q_bias', 'visual_blocks.5.attn.v_bias', 'visual_blocks.5.attn.qkv.weight', 'visual_blocks.5.attn.proj.weight', 'visual_blocks.5.attn.proj.bias', 'visual_blocks.5.norm2.weight', 'visual_blocks.5.norm2.bias', 'visual_blocks.5.mlp.fc1.weight', 'visual_blocks.5.mlp.fc1.bias', 'visual_blocks.5.mlp.fc2.weight', 'visual_blocks.5.mlp.fc2.bias', 'visual_blocks.6.norm1.weight', 'visual_blocks.6.norm1.bias', 'visual_blocks.6.attn.q_bias', 'visual_blocks.6.attn.v_bias', 'visual_blocks.6.attn.qkv.weight', 'visual_blocks.6.attn.proj.weight', 'visual_blocks.6.attn.proj.bias', 'visual_blocks.6.norm2.weight', 'visual_blocks.6.norm2.bias', 'visual_blocks.6.mlp.fc1.weight', 'visual_blocks.6.mlp.fc1.bias', 'visual_blocks.6.mlp.fc2.weight', 'visual_blocks.6.mlp.fc2.bias', 'visual_blocks.7.norm1.weight', 'visual_blocks.7.norm1.bias', 'visual_blocks.7.attn.q_bias', 'visual_blocks.7.attn.v_bias', 'visual_blocks.7.attn.qkv.weight', 'visual_blocks.7.attn.proj.weight', 'visual_blocks.7.attn.proj.bias', 'visual_blocks.7.norm2.weight', 'visual_blocks.7.norm2.bias', 'visual_blocks.7.mlp.fc1.weight', 'visual_blocks.7.mlp.fc1.bias', 'visual_blocks.7.mlp.fc2.weight', 'visual_blocks.7.mlp.fc2.bias', 'visual_blocks.8.norm1.weight', 'visual_blocks.8.norm1.bias', 'visual_blocks.8.attn.q_bias', 'visual_blocks.8.attn.v_bias', 'visual_blocks.8.attn.qkv.weight', 'visual_blocks.8.attn.proj.weight', 'visual_blocks.8.attn.proj.bias', 'visual_blocks.8.norm2.weight', 'visual_blocks.8.norm2.bias', 'visual_blocks.8.mlp.fc1.weight', 'visual_blocks.8.mlp.fc1.bias', 'visual_blocks.8.mlp.fc2.weight', 'visual_blocks.8.mlp.fc2.bias', 'visual_blocks.9.norm1.weight', 'visual_blocks.9.norm1.bias', 'visual_blocks.9.attn.q_bias', 'visual_blocks.9.attn.v_bias', 'visual_blocks.9.attn.qkv.weight', 'visual_blocks.9.attn.proj.weight', 'visual_blocks.9.attn.proj.bias', 'visual_blocks.9.norm2.weight', 'visual_blocks.9.norm2.bias', 'visual_blocks.9.mlp.fc1.weight', 'visual_blocks.9.mlp.fc1.bias', 'visual_blocks.9.mlp.fc2.weight', 'visual_blocks.9.mlp.fc2.bias', 'visual_blocks.10.norm1.weight', 'visual_blocks.10.norm1.bias', 'visual_blocks.10.attn.q_bias', 'visual_blocks.10.attn.v_bias', 'visual_blocks.10.attn.qkv.weight', 'visual_blocks.10.attn.proj.weight', 'visual_blocks.10.attn.proj.bias', 'visual_blocks.10.norm2.weight', 'visual_blocks.10.norm2.bias', 'visual_blocks.10.mlp.fc1.weight', 'visual_blocks.10.mlp.fc1.bias', 'visual_blocks.10.mlp.fc2.weight', 'visual_blocks.10.mlp.fc2.bias', 'visual_blocks.11.norm1.weight', 'visual_blocks.11.norm1.bias', 'visual_blocks.11.attn.q_bias', 'visual_blocks.11.attn.v_bias', 'visual_blocks.11.attn.qkv.weight', 'visual_blocks.11.attn.proj.weight', 'visual_blocks.11.attn.proj.bias', 'visual_blocks.11.norm2.weight', 'visual_blocks.11.norm2.bias', 'visual_blocks.11.mlp.fc1.weight', 'visual_blocks.11.mlp.fc1.bias', 'visual_blocks.11.mlp.fc2.weight', 'visual_blocks.11.mlp.fc2.bias', 'visual_fc_norm.weight', 'visual_fc_norm.bias'])